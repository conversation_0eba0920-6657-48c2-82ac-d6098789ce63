"use client"

import * as React from "react"
import { ProductFilter, FilterState, Product } from "./Filter"

// Sample data for demonstration
const SAMPLE_PRODUCTS: Product[] = [
  { id: "1", name: "<PERSON>", calories: 52, isUserProduct: true },
  { id: "2", name: "<PERSON><PERSON>", calories: 89, isUserProduct: false },
  { id: "3", name: "Orange", calories: 47, isUserProduct: true },
  { id: "4", name: "Bread", calories: 265, isUserProduct: false },
  { id: "5", name: "Chicken Breast", calories: 165, isUserProduct: true },
  { id: "6", name: "<PERSON>", calories: 130, isUserProduct: false },
]

export function ProductFilterDemo() {
  const [filteredProducts, setFilteredProducts] = React.useState<Product[]>(SAMPLE_PRODUCTS)
  const [currentFilters, setCurrentFilters] = React.useState<FilterState>({
    searchQuery: "",
    myProducts: false,
    sortBy: "name-asc",
  })

  // Apply filters to the sample data
  const applyFilters = React.useCallback((filters: FilterState) => {
    let filtered = [...SAMPLE_PRODUCTS]

    // Apply search filter
    if (filters.searchQuery) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(filters.searchQuery.toLowerCase())
      )
    }

    // Apply my products filter
    if (filters.myProducts) {
      filtered = filtered.filter(product => product.isUserProduct)
    }

    // Apply sorting
    switch (filters.sortBy) {
      case "name-asc":
        filtered.sort((a, b) => a.name.localeCompare(b.name))
        break
      case "name-desc":
        filtered.sort((a, b) => b.name.localeCompare(a.name))
        break
      case "calories-asc":
        filtered.sort((a, b) => a.calories - b.calories)
        break
      case "calories-desc":
        filtered.sort((a, b) => b.calories - a.calories)
        break
    }

    setFilteredProducts(filtered)
    setCurrentFilters(filters)
  }, [])

  return (
    <div className="w-full max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Product Filter Demo</h1>
      
      {/* Filter Component */}
      <ProductFilter
        onFilterChange={applyFilters}
        onSearch={(query) => console.log("Search:", query)}
        onMyProductsToggle={(enabled) => console.log("My Products:", enabled)}
        onSortChange={(sortBy) => console.log("Sort by:", sortBy)}
      />

      {/* Results Display */}
      <div className="mt-6">
        <h2 className="text-lg font-semibold mb-3">
          Results ({filteredProducts.length} products)
        </h2>
        
        {/* Current Filters Display */}
        <div className="mb-4 p-3 bg-gray-50 rounded-md text-sm">
          <strong>Active Filters:</strong>
          <ul className="mt-1 space-y-1">
            <li>Search: "{currentFilters.searchQuery || "None"}"</li>
            <li>My Products: {currentFilters.myProducts ? "Yes" : "No"}</li>
            <li>Sort: {currentFilters.sortBy}</li>
          </ul>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredProducts.map((product) => (
            <div
              key={product.id}
              className="border rounded-lg p-4 hover:shadow-md transition-shadow"
            >
              <h3 className="font-medium">{product.name}</h3>
              <p className="text-sm text-gray-600">{product.calories} calories</p>
              <p className="text-xs text-blue-600 mt-1">
                {product.isUserProduct ? "My Product" : "Public Product"}
              </p>
            </div>
          ))}
        </div>

        {filteredProducts.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No products found matching your criteria.
          </div>
        )}
      </div>
    </div>
  )
}
