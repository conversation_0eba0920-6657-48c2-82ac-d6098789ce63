"use client";
import React, { useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { BarChart3, Apple, Calendar, LogOut } from "lucide-react";
import { SideHeader } from "./sideHeader";

interface AppSidebarProps {
  className?: string;
}

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const AppSidebar: React.FC<AppSidebarProps> = ({ className = "" }) => {
  const pathname = usePathname();
  const { toggleSidebar, isMobile } = useSidebar();

  useEffect(() => {
    if (isMobile) {
      toggleSidebar();
    }
  }, [pathname]);

  // TODO: Replace with actual user data from your backend/auth system
  const userData = {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "", // Add actual avatar URL when available
  };

  const navigationItems: NavigationItem[] = [
    {
      href: "/dashboard",
      label: "Dashboard",
      icon: BarChart3,
    },
    {
      href: "/product",
      label: "Produkty",
      icon: Apple,
    },
    {
      href: "/meal-plan",
      label: "Jadłospisy",
      icon: Calendar,
    },
  ];

  // TODO: Implement actual logout functionality
  const handleLogout = () => {
    console.log("Logout clicked - implement actual logout logic");
  };

  return (
    <Sidebar className={className}>
      {/* Header */}
      <SidebarHeader>
        <SideHeader />
      </SidebarHeader>

      {/* User Profile */}
      <SidebarGroup>
        <SidebarGroupContent>
          <div className="flex items-center gap-3 px-2 py-2">
            <Avatar className="w-10 h-10">
              <AvatarImage src={userData.avatar} alt={userData.name} />
              <AvatarFallback className="bg-gray-100 text-gray-600 text-sm font-medium">
                {userData.name
                  .split(" ")
                  .map((n) => n[0])
                  .join("")}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col min-w-0 flex-1">
              <p className="text-sm font-medium truncate">{userData.name}</p>
              <p className="text-xs text-muted-foreground truncate">
                {userData.email}
              </p>
            </div>
          </div>
        </SidebarGroupContent>
      </SidebarGroup>

      {/* Navigation */}
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = pathname === item.href;
                return (
                  <SidebarMenuItem key={item.href}>
                    <SidebarMenuButton asChild isActive={isActive}>
                      <Link href={item.href}>
                        <Icon className="w-4 h-4" />
                        <span>{item.label}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      {/* Logout */}
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton onClick={handleLogout}>
              <LogOut className="w-4 h-4" />
              <span>Wyloguj</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AppSidebar;
