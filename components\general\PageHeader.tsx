import React from "react";
import { cn } from "@/lib/utils";

interface PageHeaderProps {
  /**
   * The main title/heading text to display
   */
  title: string;
  /**
   * Optional subtitle or description text
   */
  subtitle?: string;
  /**
   * Action components to display on the right side (e.g., buttons)
   */
  actions?: React.ReactNode;
  /**
   * Additional CSS classes to apply to the header container
   */
  className?: string;
  /**
   * Additional CSS classes to apply to the title
   */
  titleClassName?: string;
  /**
   * Additional CSS classes to apply to the subtitle
   */
  subtitleClassName?: string;
  /**
   * Additional CSS classes to apply to the actions container
   */
  actionsClassName?: string;
}

/**
 * A reusable page header component that displays a title, optional subtitle,
 * and action components (like buttons) in a consistent layout.
 *
 * @example
 * ```tsx
 * <PageHeader
 *   title="Products"
 *   actions={<Button>Add product</Button>}
 * />
 * ```
 *
 * @example
 * ```tsx
 * <PageHeader
 *   title="Dashboard"
 *   subtitle="Welcome back, <PERSON>!"
 *   actions={
 *     <div className="flex gap-2">
 *       <Button variant="outline">Export</Button>
 *       <Button>Create New</Button>
 *     </div>
 *   }
 * />
 * ```
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  actions,
  className,
  titleClassName,
  subtitleClassName,
  actionsClassName,
}) => {
  return (
    <header
      className={cn(
        "flex items-center justify-between py-4 px-6 bg-white border-b border-border",
        className
      )}
    >
      {/* Title and subtitle section */}
      <div className="flex flex-col gap-1">
        <h1
          className={cn(
            "text-2xl font-semibold text-foreground",
            titleClassName
          )}
        >
          {title}
        </h1>
        {subtitle && (
          <p className={cn("text-sm text-muted-foreground", subtitleClassName)}>
            {subtitle}
          </p>
        )}
      </div>

      {/* Actions section */}
      {actions && (
        <div className={cn("flex items-center gap-2", actionsClassName)}>
          {actions}
        </div>
      )}
    </header>
  );
};

export default PageHeader;
