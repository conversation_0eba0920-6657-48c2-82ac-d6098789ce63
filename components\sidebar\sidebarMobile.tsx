'use client'
import React from 'react'
import { SideHeader } from './sideHeader'
import { useSidebar } from '../ui/sidebar'
import { Menu } from 'lucide-react'

export const SidebarMobile = () => {
  const { toggleSidebar, isMobile } = useSidebar()
  return (
    <div className={`${isMobile ? 'flex' : 'hidden'} w-full items-center justify-between pr-2 bg-background py-2`}>
      <SideHeader/>
      <Menu onClick={() => toggleSidebar()}/>
    </div>
  )
}
