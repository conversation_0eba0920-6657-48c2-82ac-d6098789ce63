"use client";
import PageWrapper from "@/components/wrappers/authPageWrapper";
import PageHeader from "@/components/general/PageHeader";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { ProductFilter } from "@/components/product/Filter";
import { useSearchParams } from "next/navigation";
import { useEffect } from "react";

export default function ProductPage() {
  const useSearchParam = useSearchParams();

  useEffect(() => {
    console.log(useSearchParam.get('sort'));
    console.log(useSearchParam.get('searchQuery'));
    console.log(useSearchParam.get('myProducts'));
  }, [useSearchParam]);
  return (
    <PageWrapper>
      <PageHeader
        title="Produkty"
        actions={
          <Button>
            <Plus className="w-4 h-4" />
            Dodaj produkt
          </Button>
        }
      />
      <ProductFilter/>
      <div className="p-6">
        {/* Page content goes here */}
        <p className="text-muted-foreground">
          Product management content will be displayed here.
        </p>
      </div>
    </PageWrapper>
  );
}
